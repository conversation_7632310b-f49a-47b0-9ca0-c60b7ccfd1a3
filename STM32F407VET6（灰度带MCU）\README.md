# STM32F407VET6 控制项目

## 项目简介
基于STM32F407VET6的智能控制系统，集成多种传感器和执行器模块。

## 灰度传感器移植说明

### 硬件接口变更
项目已从I2C通信的带MCU灰度传感器切换为ADC+地址线切换的不带MCU灰度传感器：

**原配置（已移除）：**
- PC4: GRAY_SOFT_SCL (I2C时钟线)
- PC5: GRAY_SOFT_SDA (I2C数据线)

**新配置：**
- PB1: 模拟信号输入 (ADC12_IN9)
- PC5: 地址线AD0 (数字输出)
- PC4: 地址线AD1 (数字输出)  
- PD15: 地址线AD2 (数字输出)

### 引脚配置表
| 功能 | 引脚 | 配置 | 说明 |
|------|------|------|------|
| ADC输入 | PB1 | GPIO_MODE_ANALOG | 8路传感器模拟信号输入 |
| 地址线AD0 | PC5 | GPIO_MODE_OUTPUT_PP | 传感器通道选择位0 |
| 地址线AD1 | PC4 | GPIO_MODE_OUTPUT_PP | 传感器通道选择位1 |
| 地址线AD2 | PD15 | GPIO_MODE_OUTPUT_PP | 传感器通道选择位2 |

### 软件接口
应用层接口保持完全兼容：
- `Digtal` 变量：8位数字状态输出
- `g_line_position_error` 变量：加权位置误差计算
- `Gray_Task()` 函数：5ms周期调度任务

### 新增模块
```
User/Module/grayscale_adc/
├── adc_hal.c/h          # ADC HAL库驱动
├── grayscale_hal.c/h    # 灰度传感器HAL库驱动
```

### 配置参数
在 `mydefine.h` 中统一管理：
```c
#define GRAYSCALE_ADC_CHANNEL    ADC_CHANNEL_9  // PB1对应ADC12_IN9
#define GRAYSCALE_SAMPLE_COUNT   8              // 采样次数
```

### 故障排除
1. **ADC采样异常**：检查PB1引脚配置为模拟输入模式
2. **地址线切换问题**：确认PC4/PC5/PD15配置为推挽输出
3. **数据读取错误**：验证ADC1时钟使能和校准完成
4. **调度任务未运行**：确认Gray_Task在my_scheduler.c中已启用

## 项目结构
```
├── Core/                # STM32 HAL库核心文件
├── User/               # 用户应用代码
│   ├── App/           # 应用层
│   ├── Driver/        # 驱动层
│   └── Module/        # 模块层
│       └── grayscale_adc/  # 灰度传感器ADC模块
└── Drivers/           # STM32 HAL驱动库
```
