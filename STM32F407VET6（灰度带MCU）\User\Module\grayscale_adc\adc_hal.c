#include "adc_hal.h"

static ADC_HandleTypeDef hadc1;

// ADC1初始化 PB1->ADC12_IN9
void ADC_HAL_Init(void)
{
    __HAL_RCC_ADC1_CLK_ENABLE(); // 使能ADC1时钟
    __HAL_RCC_GPIOB_CLK_ENABLE(); // 使能GPIOB时钟
    
    // PB1配置为模拟输入
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
    
    // ADC基本配置
    hadc1.Instance = ADC1;
    hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc1.Init.Resolution = ADC_RESOLUTION_12B; // 12位分辨率
    hadc1.Init.ScanConvMode = DISABLE; // 非扫描模式
    hadc1.Init.ContinuousConvMode = DISABLE; // 单次转换
    hadc1.Init.DiscontinuousConvMode = DISABLE;
    hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE; // 软件触发
    hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT; // 右对齐
    hadc1.Init.NbrOfConversion = 1; // 1个转换通道
    hadc1.Init.DMAContinuousRequests = DISABLE;
    hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    HAL_ADC_Init(&hadc1);
    
    // 配置ADC通道9
    ADC_ChannelConfTypeDef sConfig = {0};
    sConfig.Channel = ADC_CHANNEL_9; // PB1对应ADC12_IN9
    sConfig.Rank = 1;
    sConfig.SamplingTime = ADC_SAMPLETIME_56CYCLES; // 采样时间
    HAL_ADC_ConfigChannel(&hadc1, &sConfig);
}

// 获取ADC转换值
uint16_t ADC_HAL_GetValue(void)
{
    HAL_ADC_Start(&hadc1); // 启动转换
    HAL_ADC_PollForConversion(&hadc1, HAL_MAX_DELAY); // 等待转换完成
    return (uint16_t)HAL_ADC_GetValue(&hadc1); // 返回转换结果
}
